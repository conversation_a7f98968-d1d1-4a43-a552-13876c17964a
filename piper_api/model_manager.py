#!/usr/bin/env python3
"""
Dynamic model manager for Piper TTS API.
Automatically detects available models and provides model switching functionality.
"""

import json
import logging
import time
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ModelInfo:
    """Information about a TTS model."""
    id: str
    name: str
    description: str
    version: str
    language: str
    type: str
    quality: str
    speakers: int
    sample_rate: int
    model_size_mb: float
    training_data: str
    recommended_for: str
    model_path: str
    config_path: str
    metadata_path: str
    training_epochs: Optional[int] = None

class ModelManager:
    """Manages available TTS models dynamically."""

    def __init__(self, models_dir: str = "models", auto_scan_interval: int = 30):
        self.models_dir = Path(models_dir)
        self.available_models: Dict[str, ModelInfo] = {}
        self.current_model_id: Optional[str] = None
        self.auto_scan_interval = auto_scan_interval
        self.last_scan_time = 0
        self.directory_mtime = 0
        self._scan_thread = None
        self._stop_scanning = False
        self.scan_models()
        self.start_auto_scan()
    
    def scan_models(self) -> None:
        """Scan the models directory for available models."""
        self.available_models.clear()
        
        if not self.models_dir.exists():
            logger.warning(f"Models directory not found: {self.models_dir}")
            return
        
        for model_dir in self.models_dir.iterdir():
            if not model_dir.is_dir():
                continue

            model_path = model_dir / "model.onnx"
            metadata_path = model_dir / "metadata.json"

            # Look for config file with multiple possible names
            config_path = None
            config_candidates = [
                model_dir / "config.json",      # Standard Piper format
                model_dir / "model.config",     # Alternative format
                model_dir / "config.yaml",      # YAML format
                model_dir / "model.json",       # Alternative JSON format
                model_dir / f"{model_dir.name}.json"  # Named after directory
            ]

            for candidate in config_candidates:
                if candidate.exists():
                    config_path = candidate
                    break

            # Check if required files exist
            if not model_path.exists():
                logger.warning(f"Skipping {model_dir.name}: missing model.onnx")
                continue

            if not config_path:
                logger.warning(f"Skipping {model_dir.name}: no config file found (tried: {[c.name for c in config_candidates]})")
                continue
            
            # Load config file to extract model information
            config_data = {}
            try:
                if config_path.suffix.lower() in ['.json']:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                elif config_path.suffix.lower() in ['.yaml', '.yml']:
                    try:
                        import yaml
                        with open(config_path, 'r', encoding='utf-8') as f:
                            config_data = yaml.safe_load(f)
                    except ImportError:
                        logger.warning(f"YAML config found but PyYAML not installed for {model_dir.name}")
                        continue
                else:
                    # Try to parse as JSON even if extension is different
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)

                logger.info(f"Loaded config from {config_path.name} for {model_dir.name}")

            except Exception as e:
                logger.warning(f"Failed to load config for {model_dir.name}: {e}")
                continue

            # Load metadata if available (metadata takes precedence over config for display info)
            metadata = {}
            if metadata_path.exists():
                try:
                    with open(metadata_path, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)
                except Exception as e:
                    logger.warning(f"Failed to load metadata for {model_dir.name}: {e}")

            # Merge config data with metadata (metadata overrides config)
            combined_info = {**config_data, **metadata}
            
            # Extract model information from config and metadata
            # Try to get speaker count from config if not in metadata
            speakers = combined_info.get('speakers', combined_info.get('num_speakers', 1))
            sample_rate = combined_info.get('sample_rate', combined_info.get('audio', {}).get('sample_rate', 22050))

            # Normalize language field - convert complex objects to simple strings for frontend
            language_raw = combined_info.get('language', combined_info.get('lang', 'ne_NP'))
            language = self._normalize_language(language_raw)

            # Create ModelInfo
            model_info = ModelInfo(
                id=model_dir.name,
                name=combined_info.get('name', model_dir.name.replace('_', ' ').title()),
                description=combined_info.get('description', f'TTS model: {model_dir.name}'),
                version=combined_info.get('version', '1.0.0'),
                language=language,
                type=combined_info.get('type', self._detect_model_type(model_dir.name)),
                quality=combined_info.get('quality', 'medium'),
                speakers=speakers,
                sample_rate=sample_rate,
                model_size_mb=combined_info.get('model_size_mb', model_path.stat().st_size / (1024 * 1024)),
                training_data=combined_info.get('training_data', 'Unknown'),
                recommended_for=combined_info.get('recommended_for', 'General use'),
                model_path=str(model_path),
                config_path=str(config_path),
                metadata_path=str(metadata_path),
                training_epochs=combined_info.get('training_epochs')
            )
            
            self.available_models[model_dir.name] = model_info
            logger.info(f"Found model: {model_info.name} ({model_info.id})")

    def _detect_model_type(self, model_name: str) -> str:
        """Detect model type based on directory name."""
        name_lower = model_name.lower()

        if 'finetuned' in name_lower or 'fine_tuned' in name_lower:
            return 'finetuned'
        elif 'pretrained' in name_lower or 'checkpoint' in name_lower:
            return 'pretrained'
        elif 'original' in name_lower or 'base' in name_lower:
            return 'original'
        elif 'custom' in name_lower:
            return 'custom'
        else:
            return 'unknown'

    def _normalize_language(self, language_data) -> str:
        """Normalize language data to a simple string for frontend compatibility."""
        if isinstance(language_data, dict):
            # If it's a complex language object, extract the most useful representation
            return (language_data.get('name_english') or
                   language_data.get('code') or
                   language_data.get('name_native') or
                   'Unknown')
        elif isinstance(language_data, str):
            return language_data
        else:
            return 'Unknown'

    def get_available_models(self) -> List[Dict[str, Any]]:
        """Get list of available models."""
        return [
            {
                "id": model.id,
                "name": model.name,
                "description": model.description,
                "version": model.version,
                "language": model.language,
                "type": model.type,
                "quality": model.quality,
                "speakers": model.speakers,
                "sample_rate": model.sample_rate,
                "model_size_mb": round(model.model_size_mb, 1),
                "training_data": model.training_data,
                "recommended_for": model.recommended_for,
                "training_epochs": model.training_epochs,
                "is_current": model.id == self.current_model_id
            }
            for model in self.available_models.values()
        ]
    
    def get_model_info(self, model_id: str) -> Optional[ModelInfo]:
        """Get information about a specific model."""
        return self.available_models.get(model_id)
    
    def get_model_paths(self, model_id: str) -> Optional[tuple[str, str]]:
        """Get model and config paths for a specific model."""
        model_info = self.get_model_info(model_id)
        if model_info:
            return model_info.model_path, model_info.config_path
        return None
    
    def set_current_model(self, model_id: str) -> bool:
        """Set the current active model."""
        if model_id in self.available_models:
            self.current_model_id = model_id
            return True
        return False
    
    def get_current_model(self) -> Optional[ModelInfo]:
        """Get the current active model."""
        if self.current_model_id:
            return self.available_models.get(self.current_model_id)
        return None
    
    def get_default_model_id(self) -> Optional[str]:
        """Get the recommended default model ID."""
        # Priority order: latest finetuned epochs > finetuned_custom > original_piper > pretrained_checkpoint > any other
        priority_patterns = [
            'finetuned_epoch_',  # Latest fine-tuned models
            'finetuned_custom',  # Custom fine-tuned models
            'original_piper',    # Original Piper models
            'pretrained_checkpoint'  # Pretrained checkpoints
        ]

        # Find models matching priority patterns
        for pattern in priority_patterns:
            matching_models = [model_id for model_id in self.available_models.keys()
                             if model_id.startswith(pattern)]
            if matching_models:
                # For finetuned_epoch_ models, return the one with highest epoch number
                if pattern == 'finetuned_epoch_':
                    return self._get_latest_epoch_model(matching_models)
                else:
                    return matching_models[0]

        # If none of the priority models exist, return the first available
        if self.available_models:
            return next(iter(self.available_models.keys()))

        return None

    def _get_latest_epoch_model(self, epoch_models: List[str]) -> str:
        """Get the model with the highest epoch number from a list of epoch models."""
        latest_model = epoch_models[0]
        latest_epoch = 0

        for model_id in epoch_models:
            try:
                # Extract epoch number from model_id like 'finetuned_epoch_2899'
                epoch_str = model_id.split('_')[-1]
                epoch_num = int(epoch_str)
                if epoch_num > latest_epoch:
                    latest_epoch = epoch_num
                    latest_model = model_id
            except (ValueError, IndexError):
                continue

        return latest_model
    
    def refresh_models(self) -> None:
        """Refresh the list of available models."""
        self.scan_models()

    def start_auto_scan(self) -> None:
        """Start automatic scanning for new models in a background thread."""
        if self._scan_thread is None or not self._scan_thread.is_alive():
            self._stop_scanning = False
            self._scan_thread = threading.Thread(target=self._auto_scan_worker, daemon=True)
            self._scan_thread.start()
            logger.info(f"Started auto-scanning for models every {self.auto_scan_interval} seconds")

    def stop_auto_scan(self) -> None:
        """Stop automatic scanning."""
        self._stop_scanning = True
        if self._scan_thread and self._scan_thread.is_alive():
            self._scan_thread.join(timeout=5)
            logger.info("Stopped auto-scanning for models")

    def _auto_scan_worker(self) -> None:
        """Background worker that periodically scans for new models."""
        while not self._stop_scanning:
            try:
                current_time = time.time()

                # Check if models directory has been modified
                if self.models_dir.exists():
                    current_mtime = self.models_dir.stat().st_mtime

                    # If directory was modified or enough time has passed, scan for changes
                    if (current_mtime > self.directory_mtime or
                        current_time - self.last_scan_time > self.auto_scan_interval):

                        old_model_count = len(self.available_models)
                        self.scan_models()
                        new_model_count = len(self.available_models)

                        if new_model_count != old_model_count:
                            logger.info(f"Model count changed: {old_model_count} -> {new_model_count}")

                        self.directory_mtime = current_mtime
                        self.last_scan_time = current_time

                # Sleep for a short interval before checking again
                time.sleep(5)

            except Exception as e:
                logger.error(f"Error in auto-scan worker: {e}")
                time.sleep(10)  # Wait longer on error

    def has_new_models(self) -> bool:
        """Check if there are new models available since last scan."""
        if not self.models_dir.exists():
            return False

        current_mtime = self.models_dir.stat().st_mtime
        return current_mtime > self.directory_mtime

    def get_model_stats(self) -> Dict[str, Any]:
        """Get statistics about available models."""
        stats = {
            "total_models": len(self.available_models),
            "models_by_type": {},
            "models_by_quality": {},
            "total_speakers": 0,
            "last_scan_time": self.last_scan_time,
            "auto_scan_enabled": not self._stop_scanning
        }

        for model in self.available_models.values():
            # Count by type
            model_type = model.type
            stats["models_by_type"][model_type] = stats["models_by_type"].get(model_type, 0) + 1

            # Count by quality
            quality = model.quality
            stats["models_by_quality"][quality] = stats["models_by_quality"].get(quality, 0) + 1

            # Sum speakers
            stats["total_speakers"] += model.speakers

        return stats
