version: '3.8'

services:
  piper-voice:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./models:/app/models:ro  # Mount models directory as read-only
      - ./logs:/app/logs         # Mount logs directory for persistence
    environment:
      - PYTHONUNBUFFERED=1
      - LOG_LEVEL=INFO
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Optional: Nginx reverse proxy for production
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro  # SSL certificates if needed
    depends_on:
      - piper-voice
    restart: unless-stopped
    profiles:
      - production  # Only start with --profile production
