#!/usr/bin/env python3
"""
Test script for Standalone Piper Voice TTS API
"""

import requests
import json
from pathlib import Path

API_BASE = "http://localhost:8000"

def test_health():
    """Test health endpoint."""
    print("🔍 Testing health endpoint...")
    response = requests.get(f"{API_BASE}/health")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()

def test_models():
    """Test models endpoint."""
    print("🔍 Testing models endpoint...")
    response = requests.get(f"{API_BASE}/models")
    print(f"Status: {response.status_code}")
    models = response.json()
    print(f"Found {len(models)} models:")
    for model in models:
        print(f"  - {model['id']}: {model['name']} ({model['language']})")
    print()
    return models

def test_generate(text="Hello world", model_id=None):
    """Test audio generation."""
    print(f"🔍 Testing audio generation with text: '{text}'")

    payload = {
        "text": text,
        "speed": 1.0,
        "speaker_id": 0
    }

    if model_id:
        payload["model_id"] = model_id

    response = requests.post(
        f"{API_BASE}/generate",
        json=payload,
        headers={"Content-Type": "application/json"}
    )

    print(f"Status: {response.status_code}")

    if response.status_code == 200:
        # Save audio file
        output_file = f"test_output_{model_id or 'default'}.wav"
        with open(output_file, "wb") as f:
            f.write(response.content)
        print(f"✅ Audio saved to {output_file}")
        print(f"Audio size: {len(response.content)} bytes")
    else:
        print(f"❌ Error: {response.text}")
    print()

def main():
    """Run all tests."""
    print("🧪 Standalone Piper Voice TTS API Test Suite")
    print("=" * 50)

    try:
        # Test health
        test_health()

        # Test models
        models = test_models()

        # Test with different texts and models
        test_texts = [
            "Hello world",
            "This is a test",
            "नमस्ते, यो एक परीक्षण हो।"  # Nepali text
        ]

        if models:
            # Test with each model
            for model in models:
                print(f"🎯 Testing model: {model['name']}")
                for text in test_texts[:2]:  # Test first 2 texts
                    test_generate(text, model['id'])
        else:
            print("⚠️  No models available, testing with default settings")
            for text in test_texts:
                test_generate(text)

        print("✅ All tests completed!")
        print("\n📁 Generated audio files:")
        for wav_file in Path(".").glob("test_output_*.wav"):
            print(f"   - {wav_file}")

    except requests.exceptions.ConnectionError:
        print("❌ Connection error: Is the API server running?")
        print("   Start the server with: docker compose up -d")
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    main()
