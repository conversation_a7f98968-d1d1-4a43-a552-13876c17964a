#!/usr/bin/env python3
"""
Test script for Piper Voice TTS API
"""

import requests
import json
from pathlib import Path

API_BASE = "http://localhost:8000"

def test_health():
    """Test health endpoint."""
    print("🔍 Testing health endpoint...")
    response = requests.get(f"{API_BASE}/health")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()

def test_models():
    """Test models endpoint."""
    print("🔍 Testing models endpoint...")
    response = requests.get(f"{API_BASE}/models")
    print(f"Status: {response.status_code}")
    models = response.json()
    print(f"Found {len(models)} models:")
    for model in models:
        print(f"  - {model['id']}: {model['name']} ({model['language']})")
    print()
    return models

def test_generate(model_id=None):
    """Test audio generation."""
    print("🔍 Testing audio generation...")
    
    payload = {
        "text": "नमस्ते, यो एक परीक्षण हो।",  # Nepali text
        "speed": 1.0,
        "noise_scale": 0.667
    }
    
    if model_id:
        payload["model_id"] = model_id
    
    response = requests.post(
        f"{API_BASE}/generate",
        json=payload,
        headers={"Content-Type": "application/json"}
    )
    
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        # Save audio file
        output_file = "test_output.wav"
        with open(output_file, "wb") as f:
            f.write(response.content)
        print(f"✅ Audio saved to {output_file}")
        print(f"Audio size: {len(response.content)} bytes")
    else:
        print(f"❌ Error: {response.text}")
    print()

def test_load_model(model_id):
    """Test model loading."""
    print(f"🔍 Testing model loading: {model_id}")
    response = requests.post(f"{API_BASE}/models/{model_id}/load")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()

def main():
    """Run all tests."""
    print("🧪 Piper Voice TTS API Test Suite")
    print("=" * 40)
    
    try:
        # Test health
        test_health()
        
        # Test models
        models = test_models()
        
        if models:
            # Test with first available model
            first_model = models[0]
            test_load_model(first_model["id"])
            test_generate(first_model["id"])
        else:
            print("⚠️  No models available, testing with default settings")
            test_generate()
        
        print("✅ All tests completed!")
        
    except requests.exceptions.ConnectionError:
        print("❌ Connection error: Is the API server running?")
        print("   Start the server with: python main.py")
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    main()
