#!/usr/bin/env python3
"""
Standalone Piper Voice TTS API
Clean, scalable implementation for production deployment
"""

import io
import logging
import wave
from pathlib import Path
from typing import Optional, List, Dict, Any
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

from voice_manager import VoiceManager
from model_manager import ModelManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global managers
voice_manager: Optional[VoiceManager] = None
model_manager: Optional[ModelManager] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    global voice_manager, model_manager
    
    logger.info("Starting Piper Voice TTS API")

    # Initialize managers
    model_manager = ModelManager("models")
    voice_manager = VoiceManager(model_manager)

    # Load default model if available
    available_models = model_manager.get_available_models()
    if available_models:
        default_model = available_models[0]["id"]
        await voice_manager.load_voice(default_model)
        logger.info(f"Loaded default model: {default_model}")
    else:
        logger.warning("No models found in models directory")

    yield

    logger.info("Shutting down Piper Voice TTS API")
    if voice_manager:
        voice_manager.cleanup()

# Initialize FastAPI app
app = FastAPI(
    title="Piper Voice TTS API",
    description="Standalone, scalable Text-to-Speech API using Piper",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure as needed for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request/Response models
class GenerateRequest(BaseModel):
    text: str = Field(..., description="Text to synthesize", max_length=5000)
    model_id: Optional[str] = Field(None, description="Model ID to use")
    speaker_id: Optional[int] = Field(None, description="Speaker ID (if model supports multiple speakers)")
    speed: Optional[float] = Field(1.0, description="Speech speed (0.5-2.0)", ge=0.5, le=2.0)
    noise_scale: Optional[float] = Field(0.667, description="Noise scale for variability", ge=0.0, le=1.0)
    noise_w: Optional[float] = Field(0.8, description="Noise width for prosody", ge=0.0, le=1.0)

class ModelInfo(BaseModel):
    id: str
    name: str
    description: str
    language: str
    speakers: int
    sample_rate: int
    is_current: bool

# API Endpoints
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    current_model = model_manager.get_current_model() if model_manager else None
    return {
        "status": "healthy",
        "voice_loaded": voice_manager.voice is not None if voice_manager else False,
        "current_model": current_model.name if current_model else None,
        "available_models": len(model_manager.available_models) if model_manager else 0
    }

@app.get("/models", response_model=List[ModelInfo])
async def get_models():
    """Get available models."""
    if not model_manager:
        raise HTTPException(status_code=503, detail="Model manager not initialized")
    
    models = model_manager.get_available_models()
    return [
        ModelInfo(
            id=model["id"],
            name=model["name"],
            description=model["description"],
            language=model["language"],
            speakers=model["speakers"],
            sample_rate=model["sample_rate"],
            is_current=model["is_current"]
        )
        for model in models
    ]

@app.post("/generate")
async def generate_audio(request: GenerateRequest):
    """Generate speech audio from text."""
    if not voice_manager:
        raise HTTPException(status_code=503, detail="Voice manager not initialized")
    
    logger.info(f"Generate request: text_length={len(request.text)}, model_id={request.model_id}")
    
    try:
        # Load model if specified and different from current
        if request.model_id and request.model_id != model_manager.current_model_id:
            await voice_manager.load_voice(request.model_id)
        
        # Generate audio
        audio_data = await voice_manager.synthesize(
            text=request.text,
            speaker_id=request.speaker_id,
            length_scale=1.0 / request.speed if request.speed else None,
            noise_scale=request.noise_scale,
            noise_w=request.noise_w
        )
        
        logger.info(f"Audio generated successfully: {len(audio_data)} bytes")
        
        return Response(
            content=audio_data,
            media_type="audio/wav",
            headers={
                "Content-Disposition": "attachment; filename=speech.wav",
                "X-Audio-Duration": str(len(audio_data) / (22050 * 2)),  # Approximate duration
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to generate audio: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Audio generation failed: {str(e)}")

@app.post("/models/{model_id}/load")
async def load_model(model_id: str):
    """Load a specific model."""
    if not voice_manager:
        raise HTTPException(status_code=503, detail="Voice manager not initialized")
    
    try:
        await voice_manager.load_voice(model_id)
        logger.info(f"Model loaded successfully: {model_id}")
        return {"message": f"Model {model_id} loaded successfully"}
    except Exception as e:
        logger.error(f"Failed to load model {model_id}: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Failed to load model: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
