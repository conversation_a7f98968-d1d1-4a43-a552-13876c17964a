#!/bin/bash
# Startup script for Piper Voice TTS API

set -e

echo "🚀 Starting Piper Voice TTS API"
echo "================================"

# Check if models directory exists and has models
if [ ! -d "models" ] || [ -z "$(ls -A models)" ]; then
    echo "⚠️  Warning: No models found in models/ directory"
    echo "   Please add your Piper ONNX models to the models/ directory"
    echo "   Each model should be in its own subdirectory with:"
    echo "   - model.onnx (the ONNX model file)"
    echo "   - config.json (model configuration)"
    echo "   - metadata.json (optional, model information)"
    echo ""
fi

# Create logs directory
mkdir -p logs

# Set environment variables
export PYTHONUNBUFFERED=1
export LOG_LEVEL=${LOG_LEVEL:-INFO}

echo "📍 API will be available at: http://localhost:8000"
echo "📖 API docs will be available at: http://localhost:8000/docs"
echo "🛑 Press Ctrl+C to stop the server"
echo ""

# Start the application
python main.py
