#!/usr/bin/env python3
"""
Model Manager for Piper Voice TTS
Handles model discovery, validation, and metadata management
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ModelInfo:
    """Information about a TTS model."""
    id: str
    name: str
    description: str
    language: str
    speakers: int
    sample_rate: int
    model_path: str
    config_path: str
    metadata_path: Optional[str] = None

class ModelManager:
    """Manages available TTS models."""
    
    def __init__(self, models_dir: str = "models"):
        self.models_dir = Path(models_dir)
        self.available_models: Dict[str, ModelInfo] = {}
        self.current_model_id: Optional[str] = None
        
        # Create models directory if it doesn't exist
        self.models_dir.mkdir(exist_ok=True)
        
        # Scan for models
        self.scan_models()
    
    def scan_models(self) -> None:
        """Scan the models directory for available models."""
        self.available_models.clear()
        
        if not self.models_dir.exists():
            logger.warning(f"Models directory not found: {self.models_dir}")
            return

        logger.info(f"Scanning for models in: {self.models_dir}")
        
        for model_dir in self.models_dir.iterdir():
            if not model_dir.is_dir():
                continue
                
            try:
                model_info = self._load_model_info(model_dir)
                if model_info:
                    self.available_models[model_info.id] = model_info
                    logger.debug(f"Found model: {model_info.id} - {model_info.name}")
            except Exception as e:
                logger.warning(f"Failed to load model from {model_dir}: {str(e)}")

        logger.info(f"Model scan completed: {len(self.available_models)} models found")
    
    def _load_model_info(self, model_dir: Path) -> Optional[ModelInfo]:
        """Load model information from a directory."""
        model_id = model_dir.name
        
        # Look for model files
        model_path = None
        config_path = None
        
        # Check for ONNX model
        onnx_files = list(model_dir.glob("*.onnx"))
        if onnx_files:
            model_path = onnx_files[0]
        
        # Check for config file
        config_files = list(model_dir.glob("*.json"))
        config_files.extend(list(model_dir.glob("config.json")))
        if config_files:
            config_path = config_files[0]
        
        if not model_path or not config_path:
            logger.debug(f"Missing required files in {model_dir}: model={model_path is not None}, config={config_path is not None}")
            return None
        
        # Load config to get model information
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
        except Exception as e:
            logger.warning(f"Failed to load config {config_path}: {str(e)}")
            return None
        
        # Load metadata if available
        metadata_path = model_dir / "metadata.json"
        metadata = {}
        if metadata_path.exists():
            try:
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
            except Exception as e:
                logger.warning(f"Failed to load metadata {metadata_path}: {str(e)}")
        
        # Extract model information
        sample_rate = config.get('audio', {}).get('sample_rate', 22050)
        num_speakers = config.get('num_speakers', 1)
        
        # Use metadata for additional info, fallback to defaults
        name = metadata.get('name', model_id.replace('_', ' ').title())
        description = metadata.get('description', f'TTS model: {model_id}')
        language = metadata.get('language', config.get('language', 'unknown'))
        
        return ModelInfo(
            id=model_id,
            name=name,
            description=description,
            language=language,
            speakers=num_speakers,
            sample_rate=sample_rate,
            model_path=str(model_path),
            config_path=str(config_path),
            metadata_path=str(metadata_path) if metadata_path.exists() else None
        )
    
    def get_available_models(self) -> List[Dict[str, Any]]:
        """Get list of available models."""
        return [
            {
                "id": model.id,
                "name": model.name,
                "description": model.description,
                "language": model.language,
                "speakers": model.speakers,
                "sample_rate": model.sample_rate,
                "is_current": model.id == self.current_model_id
            }
            for model in self.available_models.values()
        ]
    
    def get_model_info(self, model_id: str) -> Optional[ModelInfo]:
        """Get information about a specific model."""
        return self.available_models.get(model_id)
    
    def get_current_model(self) -> Optional[ModelInfo]:
        """Get currently selected model."""
        if self.current_model_id:
            return self.available_models.get(self.current_model_id)
        return None
    
    def refresh_models(self) -> None:
        """Refresh the model list."""
        self.scan_models()
