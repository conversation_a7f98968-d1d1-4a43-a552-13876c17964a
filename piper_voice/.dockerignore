# Git
.git
.gitignore

# Documentation
README.md
*.md

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Temporary files
tmp/
temp/
.tmp/

# Development
.env
.env.local
.env.development
.env.test
.env.production

# Testing
.pytest_cache/
.coverage
htmlcov/

# Build artifacts
build/
dist/
*.egg-info/

# Docker
Dockerfile
docker-compose*.yml
.dockerignore

# Large model files (should be mounted as volumes)
models/
*.onnx
*.bin
*.pt
*.pth
