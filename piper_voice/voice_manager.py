#!/usr/bin/env python3
"""
Voice Manager for Piper TTS
Handles voice loading, synthesis, and model management
"""

import io
import wave
import asyncio
from typing import Optional, Dict, Any
from pathlib import Path
import structlog

try:
    from piper import Piper<PERSON>oice
except ImportError:
    # Fallback for different piper installations
    try:
        from piper.voice import PiperVoice
    except ImportError:
        # Custom implementation if piper not available
        from voice import PiperVoice

logger = structlog.get_logger()

class VoiceManager:
    """Manages voice loading and synthesis operations."""
    
    def __init__(self, model_manager):
        self.model_manager = model_manager
        self.voice: Optional[PiperVoice] = None
        self.current_model_id: Optional[str] = None
        self._lock = asyncio.Lock()
        
    async def load_voice(self, model_id: str) -> None:
        """Load a voice model."""
        async with self._lock:
            if self.current_model_id == model_id and self.voice is not None:
                logger.debug("Model already loaded", model_id=model_id)
                return
                
            model_info = self.model_manager.get_model_info(model_id)
            if not model_info:
                raise ValueError(f"Model not found: {model_id}")
                
            logger.info("Loading voice model", model_id=model_id)
            
            try:
                # Load the voice using model and config paths
                self.voice = PiperVoice.load(
                    model_path=model_info.model_path,
                    config_path=model_info.config_path,
                    use_cuda=False  # Use CPU for better compatibility
                )
                self.current_model_id = model_id
                self.model_manager.current_model_id = model_id
                
                logger.info("Voice model loaded successfully", 
                           model_id=model_id, 
                           speakers=model_info.speakers)
                           
            except Exception as e:
                logger.error("Failed to load voice model", 
                           model_id=model_id, 
                           error=str(e))
                raise
    
    async def synthesize(
        self,
        text: str,
        speaker_id: Optional[int] = None,
        length_scale: Optional[float] = None,
        noise_scale: Optional[float] = None,
        noise_w: Optional[float] = None
    ) -> bytes:
        """Synthesize speech from text."""
        if not self.voice:
            raise RuntimeError("No voice model loaded")
            
        logger.debug("Synthesizing speech", 
                    text_length=len(text), 
                    speaker_id=speaker_id)
        
        try:
            # Create in-memory WAV file
            wav_buffer = io.BytesIO()
            
            with wave.open(wav_buffer, 'wb') as wav_file:
                # Run synthesis in thread pool to avoid blocking
                await asyncio.get_event_loop().run_in_executor(
                    None,
                    self.voice.synthesize,
                    text,
                    wav_file,
                    speaker_id,
                    length_scale,
                    noise_scale,
                    noise_w
                )
            
            audio_data = wav_buffer.getvalue()
            logger.debug("Speech synthesis completed", audio_size=len(audio_data))
            
            return audio_data
            
        except Exception as e:
            logger.error("Speech synthesis failed", error=str(e))
            raise
    
    def get_current_model(self) -> Optional[str]:
        """Get currently loaded model ID."""
        return self.current_model_id
    
    def is_voice_loaded(self) -> bool:
        """Check if a voice is currently loaded."""
        return self.voice is not None
    
    def cleanup(self) -> None:
        """Cleanup resources."""
        if self.voice:
            # Cleanup voice resources if needed
            self.voice = None
            self.current_model_id = None
            logger.info("Voice manager cleaned up")
