#!/usr/bin/env python3
"""
Standalone Piper TTS API - Minimal implementation
"""

import io
import json
import logging
import wave
from pathlib import Path
from typing import Optional, List, Dict, Any

import numpy as np
import onnxruntime
from fastapi import FastAPI, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global variables
models_dir = Path("models")
current_voice = None
current_model_id = None

class GenerateRequest(BaseModel):
    text: str = Field(..., max_length=1000)
    model_id: Optional[str] = None
    speaker_id: Optional[int] = Field(0, ge=0, le=50)
    speed: Optional[float] = Field(1.0, ge=0.5, le=2.0)

class ModelInfo(BaseModel):
    id: str
    name: str
    language: str
    speakers: int
    is_current: bool

class SimpleVoice:
    """Minimal Piper voice implementation"""
    
    def __init__(self, model_path: str, config_path: str):
        self.model_path = model_path
        self.config_path = config_path
        
        # Load config
        with open(config_path, 'r') as f:
            self.config = json.load(f)
        
        # Setup ONNX session
        self.session = onnxruntime.InferenceSession(
            model_path,
            providers=['CPUExecutionProvider']
        )
        
        self.sample_rate = self.config.get('audio', {}).get('sample_rate', 22050)
        self.num_speakers = self.config.get('num_speakers', 1)
    
    def synthesize(self, text: str, speaker_id: int = 0, length_scale: float = 1.0) -> bytes:
        """Generate audio from text"""
        try:
            # Simple text to phoneme conversion (basic implementation)
            phonemes = self._text_to_phonemes(text)
            
            # Prepare inputs
            phoneme_ids = np.array([phonemes], dtype=np.int64)
            phoneme_lengths = np.array([len(phonemes)], dtype=np.int64)
            scales = np.array([0.667, length_scale, 0.8], dtype=np.float32)
            
            inputs = {
                'input': phoneme_ids,
                'input_lengths': phoneme_lengths,
                'scales': scales
            }
            
            # Add speaker ID if multi-speaker model
            if self.num_speakers > 1:
                inputs['sid'] = np.array([speaker_id], dtype=np.int64)
            
            # Run inference
            outputs = self.session.run(None, inputs)
            audio = outputs[0].squeeze()
            
            # Convert to WAV
            return self._audio_to_wav(audio)
            
        except Exception as e:
            logger.error(f"Synthesis failed: {e}")
            raise
    
    def _text_to_phonemes(self, text: str) -> List[int]:
        """Convert text to phoneme IDs (simplified)"""
        # This is a very basic implementation
        # In production, you'd use proper phonemization
        phonemes = []
        for char in text.lower():
            if char.isalpha():
                phonemes.append(ord(char) - ord('a') + 1)
            elif char.isspace():
                phonemes.append(0)
            else:
                phonemes.append(27)
        return phonemes[:200]  # Limit length
    
    def _audio_to_wav(self, audio: np.ndarray) -> bytes:
        """Convert audio array to WAV bytes"""
        # Normalize and convert to 16-bit PCM
        audio = np.clip(audio, -1.0, 1.0)
        audio_int16 = (audio * 32767).astype(np.int16)
        
        # Create WAV file in memory
        wav_buffer = io.BytesIO()
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)  # Mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(self.sample_rate)
            wav_file.writeframes(audio_int16.tobytes())
        
        return wav_buffer.getvalue()

def scan_models() -> List[Dict[str, Any]]:
    """Scan for available models"""
    models = []
    
    if not models_dir.exists():
        return models
    
    for model_dir in models_dir.iterdir():
        if not model_dir.is_dir():
            continue
        
        # Look for ONNX model and config
        onnx_files = list(model_dir.glob("*.onnx"))
        json_files = list(model_dir.glob("*.json"))
        
        if not onnx_files or not json_files:
            continue
        
        try:
            # Load metadata
            config_path = json_files[0]
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            # Check for metadata file
            metadata_path = model_dir / "metadata.json"
            metadata = {}
            if metadata_path.exists():
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
            
            models.append({
                "id": model_dir.name,
                "name": metadata.get('name', model_dir.name.replace('_', ' ').title()),
                "language": metadata.get('language', config.get('language', 'unknown')),
                "speakers": config.get('num_speakers', 1),
                "is_current": model_dir.name == current_model_id,
                "model_path": str(onnx_files[0]),
                "config_path": str(config_path)
            })
        except Exception as e:
            logger.warning(f"Failed to load model {model_dir.name}: {e}")
    
    return models

def load_model(model_id: str) -> bool:
    """Load a specific model"""
    global current_voice, current_model_id
    
    models = scan_models()
    model_info = next((m for m in models if m['id'] == model_id), None)
    
    if not model_info:
        raise ValueError(f"Model not found: {model_id}")
    
    try:
        current_voice = SimpleVoice(
            model_path=model_info['model_path'],
            config_path=model_info['config_path']
        )
        current_model_id = model_id
        logger.info(f"Loaded model: {model_id}")
        return True
    except Exception as e:
        logger.error(f"Failed to load model {model_id}: {e}")
        raise

# FastAPI app
app = FastAPI(
    title="Standalone Piper TTS API",
    description="Minimal TTS API using Piper models",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup():
    """Load default model on startup"""
    models = scan_models()
    if models:
        try:
            load_model(models[0]['id'])
            logger.info(f"Loaded default model: {models[0]['id']}")
        except Exception as e:
            logger.warning(f"Failed to load default model: {e}")
    else:
        logger.warning("No models found")

@app.get("/health")
async def health():
    """Health check"""
    return {
        "status": "healthy",
        "voice_loaded": current_voice is not None,
        "current_model": current_model_id,
        "available_models": len(scan_models())
    }

@app.get("/models", response_model=List[ModelInfo])
async def get_models():
    """Get available models"""
    models = scan_models()
    return [
        ModelInfo(
            id=m['id'],
            name=m['name'],
            language=m['language'],
            speakers=m['speakers'],
            is_current=m['is_current']
        )
        for m in models
    ]

@app.post("/generate")
async def generate_audio(request: GenerateRequest):
    """Generate speech from text"""
    if not current_voice:
        raise HTTPException(status_code=503, detail="No model loaded")
    
    # Load different model if requested
    if request.model_id and request.model_id != current_model_id:
        try:
            load_model(request.model_id)
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Failed to load model: {e}")
    
    try:
        # Generate audio
        length_scale = 1.0 / request.speed if request.speed else 1.0
        audio_data = current_voice.synthesize(
            text=request.text,
            speaker_id=request.speaker_id or 0,
            length_scale=length_scale
        )
        
        logger.info(f"Generated audio: {len(audio_data)} bytes")
        
        return Response(
            content=audio_data,
            media_type="audio/wav",
            headers={"Content-Disposition": "attachment; filename=speech.wav"}
        )
        
    except Exception as e:
        logger.error(f"Audio generation failed: {e}")
        raise HTTPException(status_code=500, detail=f"Generation failed: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
