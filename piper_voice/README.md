# Piper Voice TTS API

A standalone, scalable Text-to-Speech API using Piper models, designed for production deployment with Docker.

## Features

- **Clean Architecture**: Minimal, focused implementation
- **Scalable**: Docker-based deployment with resource limits
- **Model Management**: Dynamic model loading and discovery
- **Production Ready**: Health checks, logging, monitoring
- **Secure**: Non-root container, rate limiting, security headers

## Quick Start

### 1. Setup Models

Create a `models` directory and add your Piper ONNX models:

```bash
mkdir -p models/my_model
# Copy your model files:
# - model.onnx (the ONNX model file)
# - config.json (model configuration)
# - metadata.json (optional, model information)
```

### 2. Run with Docker Compose

```bash
# Basic deployment
docker-compose up -d

# Production deployment with <PERSON>in<PERSON>
docker-compose --profile production up -d
```

### 3. Test the API

```bash
# Health check
curl http://localhost:8000/health

# List available models
curl http://localhost:8000/models

# Generate speech
curl -X POST http://localhost:8000/generate \
  -H "Content-Type: application/json" \
  -d '{"text": "Hello, this is a test"}' \
  --output speech.wav
```

## API Endpoints

### GET /health
Health check endpoint.

### GET /models
List available models with metadata.

### POST /generate
Generate speech from text.

**Request Body:**
```json
{
  "text": "Text to synthesize",
  "model_id": "optional_model_id",
  "speaker_id": 0,
  "speed": 1.0,
  "noise_scale": 0.667,
  "noise_w": 0.8
}
```

### POST /models/{model_id}/load
Load a specific model.

## Model Directory Structure

```
models/
├── model1/
│   ├── model.onnx
│   ├── config.json
│   └── metadata.json (optional)
├── model2/
│   ├── model.onnx
│   └── config.json
└── ...
```

### metadata.json Example
```json
{
  "name": "My TTS Model",
  "description": "High-quality Nepali TTS model",
  "language": "ne_NP",
  "version": "1.0.0"
}
```

## Configuration

### Environment Variables

- `LOG_LEVEL`: Logging level (DEBUG, INFO, WARNING, ERROR)
- `PYTHONUNBUFFERED`: Set to 1 for immediate log output

### Resource Limits

Configure in `docker-compose.yml`:
- Memory: 2GB limit, 512MB reservation
- CPU: 1.0 limit, 0.5 reservation

## Production Deployment

### 1. Enable Nginx Proxy
```bash
docker-compose --profile production up -d
```

### 2. SSL Configuration
1. Add SSL certificates to `./ssl/` directory
2. Uncomment HTTPS configuration in `nginx.conf`
3. Update domain name in configuration

### 3. Monitoring
- Health checks: `/health` endpoint
- Logs: `docker-compose logs -f piper-voice`
- Metrics: Integrate with Prometheus/Grafana

## Development

### Local Development
```bash
# Install dependencies
pip install -r requirements.txt

# Run directly
python main.py
```

### Adding Models
1. Copy model files to `models/new_model/`
2. API automatically detects new models
3. Use `/models/{model_id}/load` to switch models

## Scaling

### Horizontal Scaling
```yaml
# docker-compose.yml
services:
  piper-voice:
    deploy:
      replicas: 3
```

### Load Balancing
Nginx automatically load balances between replicas.

### Resource Optimization
- Adjust memory/CPU limits based on model size
- Use CPU-only inference for better compatibility
- Consider model quantization for smaller memory footprint

## Troubleshooting

### Common Issues

1. **Models not found**: Check models directory structure
2. **Memory issues**: Increase Docker memory limits
3. **Slow inference**: Check CPU allocation and model size

### Logs
```bash
# View logs
docker-compose logs -f piper-voice

# Debug mode
LOG_LEVEL=DEBUG docker-compose up
```

## License

This project is designed to work with Piper TTS models. Please check the licensing requirements for your specific models.
