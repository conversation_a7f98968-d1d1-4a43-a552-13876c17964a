import React from 'react';

const ModelSelector = ({ models, currentModel, onModelSwitch, isLoading, voiceInfo }) => {
  const getModelTypeIcon = (type) => {
    switch (type) {
      case 'original':
        return '🎯';
      case 'checkpoint':
        return '⚡';
      case 'fine-tuned':
        return '✨';
      default:
        return '🔊';
    }
  };

  const getModelTypeColor = (type) => {
    switch (type) {
      case 'original':
        return 'from-blue-500 to-blue-600';
      case 'checkpoint':
        return 'from-orange-500 to-orange-600';
      case 'fine-tuned':
        return 'from-purple-500 to-purple-600';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  return (
    <div className="glass-card p-6 relative">
      <div className="flex items-center space-x-3 mb-6">
        <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
          <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
          </svg>
        </div>
        <h2 className="text-xl font-bold text-gray-800">Voice Models</h2>
      </div>

      <div className="space-y-3">
        {models.map((model) => (
          <div
            key={model.id}
            className={`relative overflow-hidden rounded-xl border transition-all duration-300 cursor-pointer group ${
              currentModel?.id === model.id
                ? 'border-blue-400 bg-blue-50/70 shadow-xl scale-105 glass-card'
                : 'border-white/20 glass-card hover:border-white/40 hover:scale-102 hover:shadow-lg'
            }`}
            onClick={() => !isLoading && onModelSwitch(model)}
          >
            <div className="p-5">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center group-hover:from-indigo-600 group-hover:to-purple-700 transition-all duration-300">
                    <span className="text-lg text-white">{getModelTypeIcon(model.type)}</span>
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-800 text-base group-hover:text-gray-900 transition-colors">{model.name}</h3>
                    <p className="text-xs text-gray-600 font-medium">{model.type}</p>
                  </div>
                </div>
                {currentModel?.id === model.id && (
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-xs font-bold text-green-600">ACTIVE</span>
                  </div>
                )}
              </div>

              <div className="flex items-center justify-between">
                <div className="glass-card px-3 py-2 rounded-lg">
                  <span className="text-xs text-gray-600 font-medium">
                    {typeof model.language === 'object' ?
                      (model.language.name_english || model.language.code || 'Unknown') :
                      model.language}
                  </span>
                </div>
                <div className="glass-card px-3 py-2 rounded-lg bg-gradient-to-r from-blue-50 to-purple-50">
                  <span className="text-xs font-bold text-gray-800">{model.quality}</span>
                </div>
              </div>
            </div>
            
            {/* Gradient overlay for model type */}
            <div className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r ${getModelTypeColor(model.type)}`}></div>
          </div>
        ))}
      </div>

      {/* Voice Info */}
      {voiceInfo && (
        <div className="mt-6 p-4 bg-white/10 rounded-xl border border-white/20">
          <h3 className="text-sm font-medium text-gray-800 mb-2">Current Voice</h3>
          <div className="space-y-1 text-xs text-gray-600">
            <div>Sample Rate: {voiceInfo.sample_rate}Hz</div>
            <div>Speakers: {voiceInfo.num_speakers}</div>
            {voiceInfo.language && (
              <div>Language: {
                typeof voiceInfo.language === 'object' ?
                  (voiceInfo.language.name_english || voiceInfo.language.code || 'Unknown') :
                  voiceInfo.language
              }</div>
            )}
          </div>
        </div>
      )}

      {isLoading && (
        <div className="absolute inset-0 bg-white/50 backdrop-blur-sm rounded-2xl flex items-center justify-center">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            <span className="text-sm text-gray-700">Loading...</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ModelSelector;
